<?php

// Test script to simulate login form submission
define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR);

// Load our paths config file
require FCPATH . 'app/Config/Paths.php';
$paths = new Config\Paths();

// Location of the framework bootstrap file.
require rtrim($paths->systemDirectory, '\\/ ') . DIRECTORY_SEPARATOR . 'bootstrap.php';

// Load environment settings from .env files into $_SERVER and $_ENV
require_once SYSTEMPATH . 'Config/DotEnv.php';
(new CodeIgniter\Config\DotEnv(ROOTPATH))->load();

// Define ENVIRONMENT
if (! defined('ENVIRONMENT')) {
    define('ENVIRONMENT', env('CI_ENVIRONMENT', 'production'));
}

// Simulate POST request
$_SERVER['REQUEST_METHOD'] = 'POST';
$_POST['username'] = 'admin';
$_POST['password'] = 'admin123';

// Load CodeIgniter
$app = \Config\Services::codeigniter();
$app->initialize();

// Create Auth controller instance
$auth = new \App\Controllers\Auth();

echo "Testing login form submission...\n";

try {
    // Simulate the login request
    $request = \Config\Services::request();
    $response = $auth->login();
    
    if ($response instanceof \CodeIgniter\HTTP\RedirectResponse) {
        echo "✓ Login successful - Redirect response received\n";
        echo "  Redirect URL: " . $response->getHeaderLine('Location') . "\n";
    } else {
        echo "✗ Login failed - No redirect response\n";
        if (is_string($response)) {
            echo "  Response: " . substr($response, 0, 200) . "...\n";
        }
    }
    
    // Check session data
    $session = \Config\Services::session();
    $userId = $session->get('user_id');
    $userRole = $session->get('user_role');
    
    if ($userId) {
        echo "✓ Session created successfully\n";
        echo "  User ID: " . $userId . "\n";
        echo "  User Role: " . $userRole . "\n";
    } else {
        echo "✗ Session not created\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error during login test: " . $e->getMessage() . "\n";
    echo "  File: " . $e->getFile() . "\n";
    echo "  Line: " . $e->getLine() . "\n";
}

echo "\nTest completed.\n";
