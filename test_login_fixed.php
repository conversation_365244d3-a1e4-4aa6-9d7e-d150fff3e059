<?php

// Test login with fixed method detection
$url = 'http://localhost/todolist/auth/login';
$data = array(
    'username' => 'admin',
    'password' => 'admin123'
);

// Create POST data
$postdata = http_build_query($data);

// Create context
$context = stream_context_create(array(
    'http' => array(
        'method' => 'POST',
        'header' => 'Content-type: application/x-www-form-urlencoded',
        'content' => $postdata
    )
));

echo "Testing POST login request with fixed method detection...\n";
echo "URL: $url\n";
echo "Data: " . print_r($data, true) . "\n";

// Make the request
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "✗ Request failed\n";
} else {
    echo "✓ Request successful\n";
    echo "Response length: " . strlen($result) . " characters\n";
    
    // Check for different response types
    if (strpos($result, 'Invalid username or password') !== false) {
        echo "✗ Login failed - Invalid credentials error\n";
    } elseif (strpos($result, 'Please enter both username and password') !== false) {
        echo "✗ Login failed - Missing credentials error\n";
    } elseif (strpos($result, 'Welcome back') !== false) {
        echo "✓ Login successful - Welcome message found\n";
    } elseif (strpos($result, 'Admin Panel') !== false || strpos($result, 'Admin Dashboard') !== false) {
        echo "✓ Login successful - Admin panel content found\n";
    } elseif (strpos($result, 'Task Manager') !== false && strpos($result, 'Sign in') !== false) {
        echo "? Login form redisplayed - may indicate login issue\n";
    } else {
        echo "? Unclear result - showing first 500 characters:\n";
        echo substr($result, 0, 500) . "...\n";
    }
}

echo "\nTest completed.\n";
