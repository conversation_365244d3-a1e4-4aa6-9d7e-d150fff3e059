<?php

// Test create user form via HTTP request
$url = 'http://localhost/todolist/admin/users/create';

// First, let's test GET request to see if the form loads
echo "Testing GET request to create user form...\n";
$getResult = file_get_contents($url);

if ($getResult === FALSE) {
    echo "✗ GET request failed\n";
    exit(1);
} else {
    echo "✓ GET request successful\n";
    echo "Response length: " . strlen($getResult) . " characters\n";
    
    // Check if form is present
    if (strpos($getResult, 'createUserForm') !== false) {
        echo "✓ Create user form found\n";
    } else {
        echo "✗ Create user form not found\n";
    }
    
    // Check for any error messages
    if (strpos($getResult, 'alert-danger') !== false) {
        echo "⚠ Error messages found in form\n";
    }
}

// Now test POST request
echo "\nTesting POST request to create user...\n";

$postData = array(
    'first_name' => 'Test',
    'last_name' => 'User',
    'username' => 'testuser' . time(),
    'email' => 'test' . time() . '@example.com',
    'password' => 'password123',
    'confirm_password' => 'password123',
    'role_id' => '2',
    'is_active' => '1'
);

// Create POST data
$postString = http_build_query($postData);

// Create context
$context = stream_context_create(array(
    'http' => array(
        'method' => 'POST',
        'header' => 'Content-type: application/x-www-form-urlencoded',
        'content' => $postString
    )
));

echo "POST data:\n";
foreach ($postData as $key => $value) {
    if ($key === 'password' || $key === 'confirm_password') {
        echo "  " . $key . ": [HIDDEN]\n";
    } else {
        echo "  " . $key . ": " . $value . "\n";
    }
}

// Make the request
$postResult = file_get_contents($url, false, $context);

if ($postResult === FALSE) {
    echo "✗ POST request failed\n";
} else {
    echo "✓ POST request successful\n";
    echo "Response length: " . strlen($postResult) . " characters\n";
    
    // Check if it's a redirect (would be empty or contain redirect HTML)
    if (strlen($postResult) < 1000) {
        echo "✓ Appears to be a redirect response (short response)\n";
    }
    
    // Check for success indicators
    if (strpos($postResult, 'User created successfully') !== false) {
        echo "✓ Success message found\n";
    } elseif (strpos($postResult, 'alert-danger') !== false) {
        echo "✗ Error messages found\n";
        // Try to extract error messages
        preg_match_all('/<li>(.*?)<\/li>/', $postResult, $matches);
        if (!empty($matches[1])) {
            echo "Errors:\n";
            foreach ($matches[1] as $error) {
                echo "  - " . strip_tags($error) . "\n";
            }
        }
    } elseif (strpos($postResult, 'createUserForm') !== false) {
        echo "? Form redisplayed - may indicate validation errors\n";
    } else {
        echo "? Unclear result - showing first 200 characters:\n";
        echo substr($postResult, 0, 200) . "...\n";
    }
}

echo "\nTest completed.\n";
