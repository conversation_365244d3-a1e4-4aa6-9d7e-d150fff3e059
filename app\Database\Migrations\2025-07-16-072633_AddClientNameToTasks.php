<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddClientNameToTasks extends Migration
{
    public function up()
    {
        // Add client_name column to tasks table
        $this->forge->addColumn('tasks', [
            'client_name' => [
                'type'       => 'VARCHAR',
                'constraint' => 255,
                'null'       => true,
                'after'      => 'title'
            ]
        ]);
    }

    public function down()
    {
        // Remove client_name column from tasks table
        $this->forge->dropColumn('tasks', 'client_name');
    }
}
