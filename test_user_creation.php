<?php

// Test user creation functionality
define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR);

// Load our paths config file
require FCPATH . 'app/Config/Paths.php';
$paths = new Config\Paths();

// Location of the framework bootstrap file.
require rtrim($paths->systemDirectory, '\\/ ') . DIRECTORY_SEPARATOR . 'bootstrap.php';

// Load environment settings from .env files into $_SERVER and $_ENV
require_once SYSTEMPATH . 'Config/DotEnv.php';
(new CodeIgniter\Config\DotEnv(ROOTPATH))->load();

// Define ENVIRONMENT
if (! defined('ENVIRONMENT')) {
    define('ENVIRONMENT', env('CI_ENVIRONMENT', 'production'));
}

// Load CodeIgniter
$app = \Config\Services::codeigniter();
$app->initialize();

// Test UserModel and RoleModel
$userModel = new \App\Models\UserModel();
$roleModel = new \App\Models\RoleModel();

echo "Testing UserModel and RoleModel...\n";

// Check roles
$roles = $roleModel->findAll();
echo "Available roles: " . count($roles) . "\n";
foreach ($roles as $role) {
    echo "  - " . $role['name'] . " (ID: " . $role['id'] . ")\n";
}

// Test validation rules
echo "\nValidation rules:\n";
$rules = $userModel->getValidationRules();
foreach ($rules as $field => $rule) {
    echo "  " . $field . ": " . $rule . "\n";
}

// Test user creation
echo "\nTesting user creation...\n";
$testData = [
    'username' => 'testuser' . time(),
    'email' => 'test' . time() . '@example.com',
    'password' => password_hash('password123', PASSWORD_DEFAULT),
    'first_name' => 'Test',
    'last_name' => 'User',
    'role_id' => 2,
    'is_active' => true
];

echo "Test data:\n";
foreach ($testData as $key => $value) {
    if ($key === 'password') {
        echo "  " . $key . ": [HASHED]\n";
    } else {
        echo "  " . $key . ": " . $value . "\n";
    }
}

if ($userModel->insert($testData)) {
    echo "\nUser creation: SUCCESS\n";
    echo "New user ID: " . $userModel->getInsertID() . "\n";
} else {
    echo "\nUser creation: FAILED\n";
    echo "Errors:\n";
    $errors = $userModel->errors();
    foreach ($errors as $error) {
        echo "  - " . $error . "\n";
    }
}

echo "\nTest completed.\n";
