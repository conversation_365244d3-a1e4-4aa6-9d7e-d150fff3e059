<?php

namespace App\Models;

use CodeIgniter\Model;

class TaskModel extends Model
{
    protected $table            = 'tasks';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'title', 'client_name', 'description', 'assigned_to', 'assigned_by',
        'priority', 'status', 'due_date', 'completed_at', 'notes'
    ];

    protected bool $allowEmptyInserts = false;

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'title' => 'required|min_length[3]|max_length[255]',
        'assigned_to' => 'required|integer',
        'assigned_by' => 'required|integer',
        'priority' => 'required|in_list[low,medium,high,urgent]',
        'status' => 'required|in_list[pending,in_progress,completed,cancelled]'
    ];
    protected $validationMessages   = [
        'title' => [
            'required' => 'Task title is required',
            'min_length' => 'Title must be at least 3 characters'
        ]
    ];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Get tasks with user information
     */
    public function getTasksWithUsers($userId = null, $status = null)
    {
        $builder = $this->select('tasks.*,
                                 assigned_user.username as assigned_username,
                                 assigned_user.first_name as assigned_first_name,
                                 assigned_user.last_name as assigned_last_name,
                                 assigner.username as assigner_username,
                                 assigner.first_name as assigner_first_name,
                                 assigner.last_name as assigner_last_name')
                        ->join('users as assigned_user', 'assigned_user.id = tasks.assigned_to')
                        ->join('users as assigner', 'assigner.id = tasks.assigned_by');

        if ($userId) {
            $builder->where('tasks.assigned_to', $userId);
        }

        if ($status) {
            $builder->where('tasks.status', $status);
        }

        return $builder->orderBy('tasks.created_at', 'DESC')->findAll();
    }

    /**
     * Get task statistics for dashboard
     */
    public function getTaskStats()
    {
        $result = $this->select("
            COUNT(id) AS total,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) AS pending,
            SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) AS in_progress,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) AS completed,
            SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) AS cancelled
        ", false)->get();

        return $result->getRowArray();
    }

    /**
     * Get overdue tasks
     */
    public function getOverdueTasks($userId = null)
    {
        $builder = $this->where('due_date <', date('Y-m-d'))
                        ->whereIn('status', ['pending', 'in_progress']);

        if ($userId) {
            $builder->where('assigned_to', $userId);
        }

        return $builder->findAll();
    }

    /**
     * Mark task as completed
     */
    public function markCompleted($taskId)
    {
        return $this->update($taskId, [
            'status' => 'completed',
            'completed_at' => date('Y-m-d H:i:s')
        ]);
    }
}


