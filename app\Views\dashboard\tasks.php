<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-list-task me-2"></i>My Tasks
                        <?php if ($current_status): ?>
                            - <?= ucfirst(str_replace('_', ' ', $current_status)) ?>
                        <?php endif; ?>
                    </h1>
                    <p class="text-muted mb-0">Manage your assigned tasks</p>
                </div>
                <a href="/dashboard" class="btn btn-outline-primary">
                    <i class="bi bi-house me-1"></i>Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Filter Tabs -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <ul class="nav nav-pills">
                        <li class="nav-item">
                            <a class="nav-link <?= !$current_status ? 'active' : '' ?>" href="/dashboard/tasks">
                                <i class="bi bi-list me-1"></i>All Tasks
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $current_status === 'pending' ? 'active' : '' ?>" href="/dashboard/tasks/pending">
                                <i class="bi bi-clock me-1"></i>Pending
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $current_status === 'in_progress' ? 'active' : '' ?>" href="/dashboard/tasks/in_progress">
                                <i class="bi bi-arrow-clockwise me-1"></i>In Progress
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $current_status === 'completed' ? 'active' : '' ?>" href="/dashboard/tasks/completed">
                                <i class="bi bi-check-circle me-1"></i>Completed
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Tasks List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clipboard-data me-2"></i>Tasks List
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($tasks)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-inbox text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">No tasks found</h4>
                            <p class="text-muted">
                                <?php if ($current_status): ?>
                                    No tasks with status "<?= ucfirst(str_replace('_', ' ', $current_status)) ?>" found.
                                <?php else: ?>
                                    You don't have any tasks assigned yet.
                                <?php endif; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Task Details</th>
                                        <th>Priority</th>
                                        <th>Status</th>
                                        <th>Due Date</th>
                                        <th>Assigned By</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($tasks as $task): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <h6 class="mb-1"><?= esc($task['title']) ?></h6>
                                                    <?php if ($task['description']): ?>
                                                        <p class="text-muted mb-1 small"><?= esc($task['description']) ?></p>
                                                    <?php endif; ?>
                                                    <?php if ($task['notes']): ?>
                                                        <small class="text-info">
                                                            <i class="bi bi-sticky me-1"></i>
                                                            Notes: <?= esc($task['notes']) ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="priority-<?= $task['priority'] ?>">
                                                    <i class="bi bi-flag-fill me-1"></i>
                                                    <?= ucfirst($task['priority']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClass = [
                                                    'pending' => 'warning',
                                                    'in_progress' => 'info',
                                                    'completed' => 'success',
                                                    'cancelled' => 'secondary'
                                                ];
                                                ?>
                                                <span class="badge bg-<?= $statusClass[$task['status']] ?> status-badge">
                                                    <?= ucfirst(str_replace('_', ' ', $task['status'])) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($task['due_date']): ?>
                                                    <?= date('M j, Y', strtotime($task['due_date'])) ?>
                                                    <?php if ($task['due_date'] < date('Y-m-d') && $task['status'] !== 'completed'): ?>
                                                        <br><small class="text-danger">
                                                            <i class="bi bi-exclamation-triangle me-1"></i>Overdue
                                                        </small>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">No due date</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div>
                                                    <?= esc($task['assigner_first_name'] . ' ' . $task['assigner_last_name']) ?>
                                                    <br><small class="text-muted">@<?= esc($task['assigner_username']) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <?php if ($task['status'] === 'pending'): ?>
                                                        <button class="btn btn-sm btn-info" onclick="updateTaskStatus(<?= $task['id'] ?>, 'in_progress')">
                                                            <i class="bi bi-play"></i> Start
                                                        </button>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($task['status'] === 'in_progress'): ?>
                                                        <button class="btn btn-sm btn-success" onclick="updateTaskStatus(<?= $task['id'] ?>, 'completed')">
                                                            <i class="bi bi-check"></i> Complete
                                                        </button>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($task['status'] !== 'completed' && $task['status'] !== 'cancelled'): ?>
                                                        <button class="btn btn-sm btn-outline-primary" onclick="addNotes(<?= $task['id'] ?>)">
                                                            <i class="bi bi-sticky"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notes Modal -->
<div class="modal fade" id="notesModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-sticky me-2"></i>Add Notes
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="notesForm">
                    <input type="hidden" id="taskId" name="task_id">
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="4" 
                                  placeholder="Add your notes about this task..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveNotes()">Save Notes</button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function updateTaskStatus(taskId, status) {
    const statusText = status.replace('_', ' ');
    if (confirm(`Mark this task as ${statusText}?`)) {
        fetch(`/dashboard/updateTask/${taskId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `status=${status}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the task.');
        });
    }
}

function addNotes(taskId) {
    document.getElementById('taskId').value = taskId;
    document.getElementById('notes').value = '';
    const modal = new bootstrap.Modal(document.getElementById('notesModal'));
    modal.show();
}

function saveNotes() {
    const taskId = document.getElementById('taskId').value;
    const notes = document.getElementById('notes').value;
    
    fetch(`/dashboard/updateTask/${taskId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `notes=${encodeURIComponent(notes)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('notesModal'));
            modal.hide();
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while saving notes.');
    });
}
</script>
<?= $this->endSection() ?>
