<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class FixAdminPassword extends Migration
{
    public function up()
    {
        // Fix admin password - create correct hash for 'admin123'
        $correctHash = password_hash('admin123', PASSWORD_DEFAULT);

        $this->db->table('users')
                 ->where('username', 'admin')
                 ->update(['password' => $correctHash]);
    }

    public function down()
    {
        // No rollback needed for password fix
    }
}
