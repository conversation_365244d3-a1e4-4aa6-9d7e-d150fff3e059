<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\UserModel;
use CodeIgniter\HTTP\ResponseInterface;

class Auth extends BaseController
{
    protected $userModel;
    protected $session;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->session = \Config\Services::session();
    }

    public function index()
    {
        // Redirect to login if not authenticated
        if (!$this->session->get('user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        // Redirect based on user role
        $userRole = $this->session->get('user_role');
        if ($userRole === 'admin') {
            return redirect()->to(base_url('admin'));
        } else {
            return redirect()->to(base_url('dashboard'));
        }
    }

    public function login()
    {
        // If already logged in, redirect
        if ($this->session->get('user_id')) {
            return $this->index();
        }

        $data = [
            'title' => 'Login - Task Management System',
            'show_navbar' => false
        ];
		

        if ($this->request->getMethod() == 'post') {
			
			
            $username = trim($this->request->getPost('username'));
            $password = $this->request->getPost('password');

            if (empty($username) || empty($password)) {
                $data['error'] = 'Please enter both username and password';
                return view('auth/login', $data);
            }

            $user = $this->userModel->verifyUser($username, $password);

            if ($user) {
                // Update last login
                $this->userModel->update($user['id'], ['last_login' => date('Y-m-d H:i:s')]);

                // Get user role
                $userWithRole = $this->userModel->getUserWithRole($user['id']);

                // Set session data
                $sessionData = [
                    'user_id' => $user['id'],
                    'username' => $user['username'],
                    'user_role' => $userWithRole['role_name'],
                    'first_name' => $user['first_name'],
                    'last_name' => $user['last_name'],
                    'is_logged_in' => true
                ];

                $this->session->set($sessionData);

                // Redirect based on role
                if ($userWithRole['role_name'] == 'admin') {
                    return redirect()->to(base_url('admin'))->with('success', 'Welcome back, ' . $user['first_name'] . '!');
                } else {
                    return redirect()->to(base_url('dashboard'))->with('success', 'Welcome back, ' . $user['first_name'] . '!');
                }
            } else {
                $data['error'] = 'Invalid username or password. Please check your credentials.';
            }
        }

        return view('auth/login', $data);
    }

    public function logout()
    {
        $this->session->destroy();
        return redirect()->to(base_url('auth/login'))->with('success', 'You have been logged out successfully');
    }

    public function register()
    {
        // Allow registration if:
        // 1. No users exist (first user becomes admin)
        // 2. Current user is admin
        // 3. Less than 5 users exist (for initial setup)
        $userCount = $this->userModel->countAll();
        $isAdmin = $this->session->get('user_role') == 'admin';

        if ($userCount > 5 && !$isAdmin) {
            return redirect()->to(base_url('auth/login'))->with('error', 'Registration is restricted. Please contact an administrator.');
        }

        $data = [
            'title' => 'Register - Task Management System',
            'show_navbar' => false
        ];

        if ($this->request->getMethod() == 'post') {
            $userData = [
                'username' => $this->request->getPost('username'),
                'email' => $this->request->getPost('email'),
                'password' => password_hash($this->request->getPost('password'), PASSWORD_DEFAULT),
                'first_name' => $this->request->getPost('first_name'),
                'last_name' => $this->request->getPost('last_name'),
                'role_id' => $userCount === 0 ? 1 : 2, // First user is admin, others are regular users
                'is_active' => true
            ];

            if ($this->userModel->insert($userData)) {
                return redirect()->to(base_url('auth/login'))->with('success', 'Registration successful! Please login.');
            } else {
                $data['errors'] = $this->userModel->errors();
            }
        }

        return view('auth/register', $data);
    }

    public function test()
    {
        return 'Auth controller test - Time: ' . date('Y-m-d H:i:s') . '<br>' .
               'Session ID: ' . session_id() . '<br>' .
               'Base URL: ' . base_url() . '<br>' .
               'Request Method: ' . $this->request->getMethod();
    }

    public function loginTest()
    {
        if ($this->request->getMethod() == 'post') {
            $username = $this->request->getPost('username');
            $password = $this->request->getPost('password');

            $output = '<h3>Login Test Debug</h3>';
            $output .= 'Username: ' . htmlspecialchars($username) . '<br>';
            $output .= 'Password length: ' . strlen($password) . '<br>';
            $output .= 'Request method: ' . $this->request->getMethod() . '<br>';

            // Test user verification
            $user = $this->userModel->verifyUser($username, $password);
            $output .= 'User verification result: ' . ($user ? 'SUCCESS' : 'FAILED') . '<br>';

            if ($user) {
                $output .= 'User ID: ' . $user['id'] . '<br>';
                $output .= 'User role ID: ' . $user['role_id'] . '<br>';

                // Test getting user with role
                $userWithRole = $this->userModel->getUserWithRole($user['id']);
                $output .= 'User with role: ' . ($userWithRole ? $userWithRole['role_name'] : 'FAILED') . '<br>';
            }

            return $output;
        }

        // Show simple login form
        return '
        <form method="POST">
            <h3>Login Test Form</h3>
            Username: <input type="text" name="username" value="admin"><br><br>
            Password: <input type="password" name="password" value="admin123"><br><br>
            <button type="submit">Test Login</button>
        </form>';
    }
}
