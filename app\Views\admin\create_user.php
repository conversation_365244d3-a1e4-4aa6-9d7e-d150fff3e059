<?= $this->extend('layouts/admin') ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= base_url('admin/users') ?>">Users</a></li>
<li class="breadcrumb-item active">Create User</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-person-plus me-2"></i>Create New User
                    </h1>
                    <p class="text-muted mb-0">Add a new user to the system</p>
                </div>
                <a href="<?= base_url('admin/users') ?>" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-1"></i>Back to Users
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-person-badge me-2"></i>User Information
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (isset($errors) && !empty($errors)): ?>
                        <div class="alert alert-danger">
                            <h6><i class="bi bi-exclamation-triangle me-2"></i>Please fix the following errors:</h6>
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="<?= base_url('admin/users/create') ?>" id="createUserForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">
                                    <i class="bi bi-person me-1"></i>First Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="<?= isset($old['first_name']) ? esc($old['first_name']) : '' ?>" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">
                                    <i class="bi bi-person me-1"></i>Last Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="<?= isset($old['last_name']) ? esc($old['last_name']) : '' ?>" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">
                                    <i class="bi bi-at me-1"></i>Username <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?= isset($old['username']) ? esc($old['username']) : '' ?>" required>
                                <div class="form-text">Must be unique and at least 3 characters long</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">
                                    <i class="bi bi-envelope me-1"></i>Email Address <span class="text-danger">*</span>
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?= isset($old['email']) ? esc($old['email']) : '' ?>" required>
                                <div class="form-text">Must be a valid and unique email address</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">
                                    <i class="bi bi-lock me-1"></i>Password <span class="text-danger">*</span>
                                </label>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <div class="form-text">Must be at least 6 characters long</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">
                                    <i class="bi bi-lock-fill me-1"></i>Confirm Password <span class="text-danger">*</span>
                                </label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="role_id" class="form-label">
                                    <i class="bi bi-shield me-1"></i>Role <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="role_id" name="role_id" required>
                                    <option value="">Select a role</option>
                                    <?php foreach ($roles as $role): ?>
                                        <option value="<?= $role['id'] ?>" 
                                                <?= (isset($old['role_id']) && $old['role_id'] == $role['id']) ? 'selected' : '' ?>>
                                            <?= ucfirst(esc($role['name'])) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">
                                    <i class="bi bi-toggle-on me-1"></i>Account Status
                                </label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                           <?= (!isset($old['is_active']) || $old['is_active']) ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="is_active">
                                        Active Account
                                    </label>
                                    <div class="form-text">Uncheck to create an inactive account</div>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="d-flex justify-content-between">
                            <a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-person-plus me-1"></i>Create User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>User Creation Guidelines
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6><i class="bi bi-shield-check text-success me-1"></i>Admin Role</h6>
                        <small class="text-muted">Full access to all system features including user management, task management, and system settings.</small>
                    </div>
                    
                    <div class="mb-3">
                        <h6><i class="bi bi-person text-primary me-1"></i>User Role</h6>
                        <small class="text-muted">Limited access to personal dashboard and assigned tasks. Cannot manage other users or system settings.</small>
                    </div>

                    <hr>

                    <div class="mb-3">
                        <h6><i class="bi bi-key text-warning me-1"></i>Password Requirements</h6>
                        <ul class="small text-muted mb-0">
                            <li>Minimum 6 characters</li>
                            <li>Passwords are automatically encrypted</li>
                            <li>Users can change their password later</li>
                        </ul>
                    </div>

                    <div class="mb-0">
                        <h6><i class="bi bi-envelope text-info me-1"></i>Email Notifications</h6>
                        <small class="text-muted">Users will receive email notifications for task assignments and updates (if email system is configured).</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Focus on first name field when page loads
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('first_name').focus();
    });

    // Password confirmation validation
    document.getElementById('confirm_password').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const confirmPassword = this.value;
        
        if (password !== confirmPassword) {
            this.setCustomValidity('Passwords do not match');
            this.classList.add('is-invalid');
        } else {
            this.setCustomValidity('');
            this.classList.remove('is-invalid');
        }
    });

    // Form validation
    document.getElementById('createUserForm').addEventListener('submit', function(e) {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;

        if (password !== confirmPassword) {
            e.preventDefault();
            alert('Passwords do not match');
            document.getElementById('confirm_password').focus();
            return;
        }

        if (password.length < 6) {
            e.preventDefault();
            alert('Password must be at least 6 characters long');
            document.getElementById('password').focus();
            return;
        }

        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>Creating User...';
        submitBtn.disabled = true;
    });

    // Username validation (real-time)
    document.getElementById('username').addEventListener('input', function() {
        const username = this.value;
        if (username.length > 0 && username.length < 3) {
            this.setCustomValidity('Username must be at least 3 characters long');
            this.classList.add('is-invalid');
        } else {
            this.setCustomValidity('');
            this.classList.remove('is-invalid');
        }
    });
</script>
<?= $this->endSection() ?>
