<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Welcome back, <?= $user['name'] ?>!</h1>
                    <p class="text-muted mb-0">Here's what's happening with your tasks today.</p>
                </div>
                <div class="text-end">
                    <small class="text-muted">
                        <i class="bi bi-calendar3 me-1"></i>
                        <?= date('l, F j, Y') ?>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="bi bi-list-task text-primary" style="font-size: 2rem;"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5 class="card-title mb-0"><?= $stats['total'] ?? 0 ?></h5>
                            <p class="card-text text-muted mb-0">Total Tasks</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="bi bi-clock text-warning" style="font-size: 2rem;"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5 class="card-title mb-0"><?= $stats['pending'] ?? 0 ?></h5>
                            <p class="card-text text-muted mb-0">Pending</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="bi bi-arrow-clockwise text-info" style="font-size: 2rem;"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5 class="card-title mb-0"><?= $stats['in_progress'] ?? 0 ?></h5>
                            <p class="card-text text-muted mb-0">In Progress</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5 class="card-title mb-0"><?= $stats['completed'] ?? 0 ?></h5>
                            <p class="card-text text-muted mb-0">Completed</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Tasks -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-ul me-2"></i>Recent Tasks
                    </h5>
                    <a href="/dashboard/tasks" class="btn btn-sm btn-outline-primary">
                        View All <i class="bi bi-arrow-right ms-1"></i>
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($tasks)): ?>
                        <div class="text-center py-4">
                            <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-3">No tasks assigned yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Task</th>
                                        <th>Priority</th>
                                        <th>Status</th>
                                        <th>Due Date</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($tasks, 0, 5) as $task): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?= esc($task['title']) ?></strong>
                                                    <?php if ($task['description']): ?>
                                                        <br><small class="text-muted"><?= esc(substr($task['description'], 0, 50)) ?>...</small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="priority-<?= $task['priority'] ?>">
                                                    <i class="bi bi-flag-fill me-1"></i>
                                                    <?= ucfirst($task['priority']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClass = [
                                                    'pending' => 'warning',
                                                    'in_progress' => 'info',
                                                    'completed' => 'success',
                                                    'cancelled' => 'secondary'
                                                ];
                                                ?>
                                                <span class="badge bg-<?= $statusClass[$task['status']] ?> status-badge">
                                                    <?= ucfirst(str_replace('_', ' ', $task['status'])) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($task['due_date']): ?>
                                                    <?= date('M j, Y', strtotime($task['due_date'])) ?>
                                                    <?php if ($task['due_date'] < date('Y-m-d') && $task['status'] !== 'completed'): ?>
                                                        <br><small class="text-danger">Overdue</small>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">No due date</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($task['status'] !== 'completed'): ?>
                                                    <button class="btn btn-sm btn-success" onclick="markCompleted(<?= $task['id'] ?>)">
                                                        <i class="bi bi-check"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Overdue Tasks -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle text-danger me-2"></i>Overdue Tasks
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($overdue_tasks)): ?>
                        <div class="text-center py-3">
                            <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-0">No overdue tasks!</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($overdue_tasks as $task): ?>
                            <div class="border-start border-danger border-3 ps-3 mb-3">
                                <h6 class="mb-1"><?= esc($task['title']) ?></h6>
                                <small class="text-muted">
                                    Due: <?= date('M j, Y', strtotime($task['due_date'])) ?>
                                </small>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function markCompleted(taskId) {
    if (confirm('Mark this task as completed?')) {
        fetch(`/dashboard/updateTask/${taskId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'status=completed'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the task.');
        });
    }
}
</script>
<?= $this->endSection() ?>
