<?php

// Simple test script to debug login issues
define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR);

// Load our paths config file
require FCPATH . 'app/Config/Paths.php';
$paths = new Config\Paths();

// Location of the framework bootstrap file.
require rtrim($paths->systemDirectory, '\\/ ') . DIRECTORY_SEPARATOR . 'bootstrap.php';

// Load environment settings from .env files into $_SERVER and $_ENV
require_once SYSTEMPATH . 'Config/DotEnv.php';
(new CodeIgniter\Config\DotEnv(ROOTPATH))->load();

// Define ENVIRONMENT
if (! defined('ENVIRONMENT')) {
    define('ENVIRONMENT', env('CI_ENVIRONMENT', 'production'));
}

// Load CodeIgniter
$app = \Config\Services::codeigniter();
$app->initialize();

// Get database connection
$db = \Config\Database::connect();

// Test database connection
echo "Testing database connection...\n";
try {
    $query = $db->query("SELECT 1");
    echo "✓ Database connection successful\n";
} catch (Exception $e) {
    echo "✗ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Get admin user from database
echo "\nTesting admin user retrieval...\n";
$query = $db->query("SELECT * FROM users WHERE username = 'admin'");
$user = $query->getRowArray();

if ($user) {
    echo "✓ Admin user found\n";
    echo "  Username: " . $user['username'] . "\n";
    echo "  Email: " . $user['email'] . "\n";
    echo "  Role ID: " . $user['role_id'] . "\n";
    echo "  Is Active: " . ($user['is_active'] ? 'Yes' : 'No') . "\n";
    echo "  Password Hash: " . substr($user['password'], 0, 20) . "...\n";
} else {
    echo "✗ Admin user not found\n";
    exit(1);
}

// Test password verification
echo "\nTesting password verification...\n";
$testPassword = 'admin123';
$isValid = password_verify($testPassword, $user['password']);

if ($isValid) {
    echo "✓ Password verification successful\n";
} else {
    echo "✗ Password verification failed\n";
    
    // Try to create a new hash and compare
    echo "\nTesting new password hash...\n";
    $newHash = password_hash($testPassword, PASSWORD_DEFAULT);
    echo "New hash: " . substr($newHash, 0, 20) . "...\n";
    
    $newVerify = password_verify($testPassword, $newHash);
    echo "New hash verification: " . ($newVerify ? 'SUCCESS' : 'FAILED') . "\n";
}

// Test UserModel
echo "\nTesting UserModel...\n";
try {
    $userModel = new \App\Models\UserModel();
    $verifiedUser = $userModel->verifyUser('admin', 'admin123');
    
    if ($verifiedUser) {
        echo "✓ UserModel verification successful\n";
        echo "  User ID: " . $verifiedUser['id'] . "\n";
        echo "  Username: " . $verifiedUser['username'] . "\n";
    } else {
        echo "✗ UserModel verification failed\n";
    }
} catch (Exception $e) {
    echo "✗ UserModel error: " . $e->getMessage() . "\n";
}

// Test getUserWithRole
echo "\nTesting getUserWithRole...\n";
try {
    $userWithRole = $userModel->getUserWithRole($user['id']);
    
    if ($userWithRole) {
        echo "✓ getUserWithRole successful\n";
        echo "  Role Name: " . $userWithRole['role_name'] . "\n";
    } else {
        echo "✗ getUserWithRole failed\n";
    }
} catch (Exception $e) {
    echo "✗ getUserWithRole error: " . $e->getMessage() . "\n";
}

echo "\nTest completed.\n";
