<?= $this->extend('layouts/admin') ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item active">Tasks</li>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-list-task me-2"></i>Manage Tasks
                    </h1>
                    <p class="text-muted mb-0">View and manage all tasks in the system</p>
                </div>
                <div>
                    <a href="/admin/tasks/create" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>Create Task
                    </a>
                    <a href="/admin" class="btn btn-outline-secondary ms-2">
                        <i class="bi bi-arrow-left me-1"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Tasks List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clipboard-data me-2"></i>All Tasks
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($tasks)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-inbox text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">No tasks found</h4>
                            <p class="text-muted">No tasks have been created yet.</p>
                            <a href="/admin/tasks/create" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-1"></i>Create First Task
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>Task Details</th>
                                        <th>Assigned To</th>
                                        <th>Assigned By</th>
                                        <th>Priority</th>
                                        <th>Status</th>
                                        <th>Due Date</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($tasks as $task): ?>
                                        <tr>
                                            <td>
                                                <span class="badge bg-light text-dark">#<?= $task['id'] ?></span>
                                            </td>
                                            <td>
                                                <div>
                                                    <h6 class="mb-1"><?= esc($task['title']) ?></h6>
                                                    <?php if ($task['description']): ?>
                                                        <p class="text-muted mb-0 small"><?= esc(substr($task['description'], 0, 100)) ?>...</p>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <?= esc($task['assigned_first_name'] . ' ' . $task['assigned_last_name']) ?>
                                                    <br><small class="text-muted">@<?= esc($task['assigned_username']) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <?= esc($task['assigner_first_name'] . ' ' . $task['assigner_last_name']) ?>
                                                    <br><small class="text-muted">@<?= esc($task['assigner_username']) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="priority-<?= $task['priority'] ?>">
                                                    <i class="bi bi-flag-fill me-1"></i>
                                                    <?= ucfirst($task['priority']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClass = [
                                                    'pending' => 'warning',
                                                    'in_progress' => 'info',
                                                    'completed' => 'success',
                                                    'cancelled' => 'secondary'
                                                ];
                                                ?>
                                                <span class="badge bg-<?= $statusClass[$task['status']] ?> status-badge">
                                                    <?= ucfirst(str_replace('_', ' ', $task['status'])) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($task['due_date']): ?>
                                                    <?= date('M j, Y', strtotime($task['due_date'])) ?>
                                                    <?php if ($task['due_date'] < date('Y-m-d') && $task['status'] !== 'completed'): ?>
                                                        <br><small class="text-danger">
                                                            <i class="bi bi-exclamation-triangle me-1"></i>Overdue
                                                        </small>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('M j, Y', strtotime($task['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="/admin/tasks/edit/<?= $task['id'] ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <button class="btn btn-sm btn-outline-danger" 
                                                            onclick="deleteTask(<?= $task['id'] ?>, '<?= esc($task['title']) ?>')">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function deleteTask(taskId, taskTitle) {
    if (confirm(`Are you sure you want to delete the task "${taskTitle}"?\n\nThis action cannot be undone.`)) {
        window.location.href = `/admin/tasks/delete/${taskId}`;
    }
}

// Add some interactive features
document.addEventListener('DOMContentLoaded', function() {
    // Add tooltips to action buttons
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
<?= $this->endSection() ?>
