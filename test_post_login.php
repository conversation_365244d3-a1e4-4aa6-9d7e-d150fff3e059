<?php

// Test POST login request
$url = 'http://localhost/todolist/auth/login';
$data = array(
    'username' => 'admin',
    'password' => 'admin123'
);

// Create POST data
$postdata = http_build_query($data);

// Create context
$context = stream_context_create(array(
    'http' => array(
        'method' => 'POST',
        'header' => 'Content-type: application/x-www-form-urlencoded',
        'content' => $postdata
    )
));

echo "Testing POST login request...\n";
echo "URL: $url\n";
echo "Data: " . print_r($data, true) . "\n";

// Make the request
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "✗ Request failed\n";
} else {
    echo "✓ Request successful\n";
    echo "Response length: " . strlen($result) . " characters\n";
    
    // Check if it's a redirect (HTML with meta refresh or JavaScript redirect)
    if (strpos($result, 'window.location') !== false || strpos($result, 'meta http-equiv="refresh"') !== false) {
        echo "✓ Appears to be a redirect response\n";
    }
    
    // Check for error messages
    if (strpos($result, 'Invalid username or password') !== false) {
        echo "✗ Login failed - Invalid credentials error\n";
    } elseif (strpos($result, 'Please enter both username and password') !== false) {
        echo "✗ Login failed - Missing credentials error\n";
    } elseif (strpos($result, 'Welcome back') !== false) {
        echo "✓ Login successful - Welcome message found\n";
    } elseif (strpos($result, 'admin') !== false && strpos($result, 'dashboard') !== false) {
        echo "✓ Login appears successful - Admin dashboard content found\n";
    } else {
        echo "? Unclear result - showing first 500 characters:\n";
        echo substr($result, 0, 500) . "...\n";
    }
}

echo "\nTest completed.\n";
