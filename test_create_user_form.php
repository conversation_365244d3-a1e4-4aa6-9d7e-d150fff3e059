<?php

// Test create user form submission
define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR);

// Load our paths config file
require FCPATH . 'app/Config/Paths.php';
$paths = new Config\Paths();

// Location of the framework bootstrap file.
require rtrim($paths->systemDirectory, '\\/ ') . DIRECTORY_SEPARATOR . 'bootstrap.php';

// Load environment settings from .env files into $_SERVER and $_ENV
require_once SYSTEMPATH . 'Config/DotEnv.php';
(new CodeIgniter\Config\DotEnv(ROOTPATH))->load();

// Define ENVIRONMENT
if (! defined('ENVIRONMENT')) {
    define('ENVIRONMENT', env('CI_ENVIRONMENT', 'production'));
}

// Simulate POST request
$_SERVER['REQUEST_METHOD'] = 'POST';
$_POST['first_name'] = 'John';
$_POST['last_name'] = 'Doe';
$_POST['username'] = 'johndoe' . time();
$_POST['email'] = 'john' . time() . '@example.com';
$_POST['password'] = 'password123';
$_POST['confirm_password'] = 'password123';
$_POST['role_id'] = '2';
$_POST['is_active'] = '1';

// Load CodeIgniter
$app = \Config\Services::codeigniter();
$app->initialize();

// Simulate admin session
$session = \Config\Services::session();
$session->set([
    'user_id' => 1,
    'username' => 'admin',
    'user_role' => 'admin',
    'first_name' => 'System',
    'last_name' => 'Administrator',
    'is_logged_in' => true
]);

echo "Testing create user form submission...\n";
echo "POST data:\n";
foreach ($_POST as $key => $value) {
    if ($key === 'password' || $key === 'confirm_password') {
        echo "  " . $key . ": [HIDDEN]\n";
    } else {
        echo "  " . $key . ": " . $value . "\n";
    }
}

try {
    // Create Admin controller instance
    $admin = new \App\Controllers\Admin();
    
    echo "\nCalling createUser method...\n";
    $response = $admin->createUser();
    
    if ($response instanceof \CodeIgniter\HTTP\RedirectResponse) {
        echo "✓ Success - Redirect response received\n";
        echo "  Redirect URL: " . $response->getHeaderLine('Location') . "\n";
        
        // Check for success message
        $flashData = $session->getFlashdata();
        if (isset($flashData['success'])) {
            echo "  Success message: " . $flashData['success'] . "\n";
        }
    } else {
        echo "✗ No redirect response - checking for errors\n";
        if (is_string($response)) {
            // Check if response contains error messages
            if (strpos($response, 'alert-danger') !== false) {
                echo "  Form contains validation errors\n";
                // Extract error messages
                preg_match_all('/<li>(.*?)<\/li>/', $response, $matches);
                if (!empty($matches[1])) {
                    echo "  Errors found:\n";
                    foreach ($matches[1] as $error) {
                        echo "    - " . strip_tags($error) . "\n";
                    }
                }
            } else {
                echo "  Form displayed without errors\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "✗ Error during form submission: " . $e->getMessage() . "\n";
    echo "  File: " . $e->getFile() . "\n";
    echo "  Line: " . $e->getLine() . "\n";
}

echo "\nTest completed.\n";
