<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\TaskModel;
use App\Models\UserModel;
use CodeIgniter\HTTP\ResponseInterface;

class Dashboard extends BaseController
{
    protected $taskModel;
    protected $userModel;
    protected $session;

    public function __construct()
    {
        $this->taskModel = new TaskModel();
        $this->userModel = new UserModel();
        $this->session = \Config\Services::session();
    }

    public function index()
    {
        // Check if user is logged in
        if (!$this->session->get('user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        // Check if user is admin (redirect to admin panel)
        if ($this->session->get('user_role') === 'admin') {
            return redirect()->to(base_url('admin'));
        }

        $userId = $this->session->get('user_id');

        // Get user's tasks and statistics
        $data = [
            'title' => 'Dashboard - Task Management System',
            'user' => [
                'id' => $userId,
                'name' => $this->session->get('first_name') . ' ' . $this->session->get('last_name'),
                'username' => $this->session->get('username')
            ],
            'tasks' => $this->taskModel->getTasksWithUsers($userId),
            'stats' => $this->taskModel->getTaskStats($userId),
            'overdue_tasks' => $this->taskModel->getOverdueTasks($userId)
        ];

        return view('dashboard/index', $data);
    }

    public function tasks($status = null)
    {
        if (!$this->session->get('user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $userId = $this->session->get('user_id');

        $data = [
            'title' => 'My Tasks - Task Management System',
            'tasks' => $this->taskModel->getTasksWithUsers($userId, $status),
            'current_status' => $status
        ];

        return view('dashboard/tasks', $data);
    }

    public function updateTask($taskId)
    {
        if (!$this->session->get('user_id')) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->session->get('user_id');

        // Verify task belongs to user
        $task = $this->taskModel->where('id', $taskId)
                               ->where('assigned_to', $userId)
                               ->first();

        if (!$task) {
            return $this->response->setJSON(['success' => false, 'message' => 'Task not found']);
        }

        if ($this->request->getMethod() === 'POST') {
            $status = $this->request->getPost('status');
            $notes = $this->request->getPost('notes');

            $updateData = [
                'status' => $status,
                'notes' => $notes
            ];

            if ($status === 'completed') {
                $updateData['completed_at'] = date('Y-m-d H:i:s');
            }

            if ($this->taskModel->update($taskId, $updateData)) {
                return $this->response->setJSON(['success' => true, 'message' => 'Task updated successfully']);
            } else {
                return $this->response->setJSON(['success' => false, 'message' => 'Failed to update task']);
            }
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
    }

    public function profile()
    {
        if (!$this->session->get('user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $userId = $this->session->get('user_id');
        $user = $this->userModel->find($userId);

        $data = [
            'title' => 'Profile - Task Management System',
            'user' => $user
        ];

        if ($this->request->getMethod() === 'POST') {
            $updateData = [
                'first_name' => $this->request->getPost('first_name'),
                'last_name' => $this->request->getPost('last_name'),
                'email' => $this->request->getPost('email')
            ];

            // Update password if provided
            $newPassword = $this->request->getPost('new_password');
            if (!empty($newPassword)) {
                $updateData['password'] = password_hash($newPassword, PASSWORD_DEFAULT);
            }

            if ($this->userModel->update($userId, $updateData)) {
                // Update session data
                $this->session->set([
                    'first_name' => $updateData['first_name'],
                    'last_name' => $updateData['last_name']
                ]);

                return redirect()->to(base_url('dashboard/profile'))->with('success', 'Profile updated successfully');
            } else {
                $data['errors'] = $this->userModel->errors();
            }
        }

        return view('dashboard/profile', $data);
    }
}
