<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\TaskModel;
use App\Models\UserModel;
use App\Models\RoleModel;
use CodeIgniter\HTTP\ResponseInterface;

class Admin extends BaseController
{
    protected $taskModel;
    protected $userModel;
    protected $roleModel;
    protected $session;

    public function __construct()
    {
        $this->taskModel = new TaskModel();
        $this->userModel = new UserModel();
        $this->roleModel = new RoleModel();
        $this->session = \Config\Services::session();
    }

    private function checkAdminAccess()
    {
        if (!$this->session->get('user_id') || $this->session->get('user_role') !== 'admin') {
            return redirect()->to(base_url('auth/login'))->with('error', 'Access denied. Admin privileges required.');
        }
        return null;
    }

    public function index()
    {
        $redirect = $this->checkAdminAccess();
        if ($redirect) return $redirect;

        // Get dashboard statistics
        $data = [
            'title' => 'Admin Dashboard - Task Management System',
            'total_users' => $this->userModel->countAll(),
            'total_tasks' => $this->taskModel->countAll(),
            'task_stats' => $this->taskModel->getTaskStats(),
            'recent_tasks' => $this->taskModel->getTasksWithUsers(null, null),
            'recent_users' => $this->userModel->getAllUsersWithRoles()
        ];

        // Limit recent items
        $data['recent_tasks'] = array_slice($data['recent_tasks'], 0, 5);
        $data['recent_users'] = array_slice($data['recent_users'], 0, 5);

        return view('admin/dashboard', $data);
    }

    public function users()
    {
        $redirect = $this->checkAdminAccess();
        if ($redirect) return $redirect;

        $data = [
            'title' => 'Manage Users - Admin Panel',
            'users' => $this->userModel->getAllUsersWithRoles()
        ];

        return view('admin/users', $data);
    }

    public function createUser()
    {
        $redirect = $this->checkAdminAccess();
        if ($redirect) return $redirect;

        $data = [
            'title' => 'Create User - Admin Panel',
            'roles' => $this->roleModel->findAll()
        ];

        if (strtolower($this->request->getMethod()) == 'POST') {
            // Debug: Log the POST data
            log_message('debug', 'Create User POST data: ' . json_encode($this->request->getPost()));

            $userData = [
                'username' => $this->request->getPost('username'),
                'email' => $this->request->getPost('email'),
                'password' => password_hash($this->request->getPost('password'), PASSWORD_DEFAULT),
                'first_name' => $this->request->getPost('first_name'),
                'last_name' => $this->request->getPost('last_name'),
                'role_id' => $this->request->getPost('role_id'),
                'is_active' => $this->request->getPost('is_active') ? true : false
            ];

            log_message('debug', 'User data to insert: ' . json_encode($userData));

            if ($this->userModel->insert($userData)) {
                log_message('debug', 'User created successfully with ID: ' . $this->userModel->getInsertID());
                return redirect()->to(base_url('admin/users'))->with('success', 'User created successfully!');
            } else {
                log_message('error', 'User creation failed: ' . json_encode($this->userModel->errors()));
                $data['errors'] = $this->userModel->errors();
                $data['old'] = $this->request->getPost();
            }
        }

        return view('admin/create_user', $data);
    }

    public function tasks()
    {
        $redirect = $this->checkAdminAccess();
        if ($redirect) return $redirect;

        $data = [
            'title' => 'Manage Tasks - Admin Panel',
            'tasks' => $this->taskModel->getTasksWithUsers()
        ];

        return view('admin/tasks', $data);
    }

    public function createTask()
    {
        $redirect = $this->checkAdminAccess();
        if ($redirect) return $redirect;

        $data = [
            'title' => 'Create Task - Admin Panel',
            'users' => $this->userModel->getUsersByRole('user')
        ];

        if (strtolower($this->request->getMethod()) == 'post') {
            $taskData = [
                'title' => $this->request->getPost('title'),
                'client_name' => $this->request->getPost('client_name'),
                'description' => $this->request->getPost('description'),
                'assigned_to' => $this->request->getPost('assigned_to'),
                'assigned_by' => $this->session->get('user_id'),
                'priority' => $this->request->getPost('priority'),
                'status' => 'pending',
                'due_date' => $this->request->getPost('due_date') ?: null
            ];

            if ($this->taskModel->insert($taskData)) {
                return redirect()->to(base_url('admin/tasks'))->with('success', 'Task created and assigned successfully!');
            } else {
                $data['errors'] = $this->taskModel->errors();
                $data['old'] = $this->request->getPost();
            }
        }

        return view('admin/create_task', $data);
    }

    public function editTask($taskId)
    {
        $redirect = $this->checkAdminAccess();
        if ($redirect) return $redirect;

        $task = $this->taskModel->find($taskId);
        if (!$task) {
            return redirect()->to(base_url('admin/tasks'))->with('error', 'Task not found');
        }

        $data = [
            'title' => 'Edit Task - Admin Panel',
            'task' => $task,
            'users' => $this->userModel->getUsersByRole('user')
        ];

        if (strtolower($this->request->getMethod()) == 'post') {
            $taskData = [
                'title' => $this->request->getPost('title'),
                'description' => $this->request->getPost('description'),
                'assigned_to' => $this->request->getPost('assigned_to'),
                'priority' => $this->request->getPost('priority'),
                'status' => $this->request->getPost('status'),
                'due_date' => $this->request->getPost('due_date') ?: null
            ];

            if ($this->taskModel->update($taskId, $taskData)) {
                return redirect()->to(base_url('admin/tasks'))->with('success', 'Task updated successfully!');
            } else {
                $data['errors'] = $this->taskModel->errors();
            }
        }

        return view('admin/edit_task', $data);
    }

    public function deleteTask($taskId)
    {
        $redirect = $this->checkAdminAccess();
        if ($redirect) return $redirect;

        if ($this->taskModel->delete($taskId)) {
            return redirect()->to(base_url('admin/tasks'))->with('success', 'Task deleted successfully!');
        } else {
            return redirect()->to(base_url('admin/tasks'))->with('error', 'Failed to delete task');
        }
    }
}
