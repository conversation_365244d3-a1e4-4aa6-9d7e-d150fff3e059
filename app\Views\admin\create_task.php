<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-plus-circle me-2"></i>Create New Task
                    </h1>
                    <p class="text-muted mb-0">Assign a new task to a user</p>
                </div>
                <a href="/admin/tasks" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-1"></i>Back to Tasks
                </a>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clipboard-plus me-2"></i>Task Details
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (isset($errors) && !empty($errors)): ?>
                        <div class="alert alert-danger" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>Please fix the following errors:</strong>
                            <ul class="mb-0 mt-2">
                                <?php foreach ($errors as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="/admin/tasks/create">
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="title" class="form-label">
                                    <i class="bi bi-card-text me-1"></i>Task Title *
                                </label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       value="<?= old('title', $old['title'] ?? '') ?>" required>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="priority" class="form-label">
                                    <i class="bi bi-flag me-1"></i>Priority *
                                </label>
                                <select class="form-select" id="priority" name="priority" required>
                                    <option value="">Select Priority</option>
                                    <option value="low" <?= old('priority', $old['priority'] ?? '') === 'low' ? 'selected' : '' ?>>
                                        Low
                                    </option>
                                    <option value="medium" <?= old('priority', $old['priority'] ?? '') === 'medium' ? 'selected' : '' ?>>
                                        Medium
                                    </option>
                                    <option value="high" <?= old('priority', $old['priority'] ?? '') === 'high' ? 'selected' : '' ?>>
                                        High
                                    </option>
                                    <option value="urgent" <?= old('priority', $old['priority'] ?? '') === 'urgent' ? 'selected' : '' ?>>
                                        Urgent
                                    </option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">
                                <i class="bi bi-file-text me-1"></i>Description
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="4" 
                                      placeholder="Provide detailed task description..."><?= old('description', $old['description'] ?? '') ?></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="assigned_to" class="form-label">
                                    <i class="bi bi-person me-1"></i>Assign To *
                                </label>
                                <select class="form-select" id="assigned_to" name="assigned_to" required>
                                    <option value="">Select User</option>
                                    <?php foreach ($users as $user): ?>
                                        <option value="<?= $user['id'] ?>" 
                                                <?= old('assigned_to', $old['assigned_to'] ?? '') == $user['id'] ? 'selected' : '' ?>>
                                            <?= esc($user['first_name'] . ' ' . $user['last_name']) ?> 
                                            (@<?= esc($user['username']) ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="due_date" class="form-label">
                                    <i class="bi bi-calendar me-1"></i>Due Date
                                </label>
                                <input type="date" class="form-control" id="due_date" name="due_date" 
                                       value="<?= old('due_date', $old['due_date'] ?? '') ?>" 
                                       min="<?= date('Y-m-d') ?>">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="bg-light p-3 rounded mb-3">
                                    <h6 class="mb-2">
                                        <i class="bi bi-info-circle me-1"></i>Task Assignment Info
                                    </h6>
                                    <ul class="mb-0 small text-muted">
                                        <li>The task will be assigned to the selected user immediately</li>
                                        <li>The user will be able to see this task in their dashboard</li>
                                        <li>You can edit or delete this task later if needed</li>
                                        <li>Due date is optional but recommended for better task management</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="/admin/tasks" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-1"></i>Create Task
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Set minimum date to today
    document.addEventListener('DOMContentLoaded', function() {
        const dueDateInput = document.getElementById('due_date');
        const today = new Date().toISOString().split('T')[0];
        dueDateInput.setAttribute('min', today);
        
        // Focus on title field
        document.getElementById('title').focus();
    });

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const title = document.getElementById('title').value.trim();
        const assignedTo = document.getElementById('assigned_to').value;
        const priority = document.getElementById('priority').value;

        if (!title) {
            e.preventDefault();
            alert('Please enter a task title');
            document.getElementById('title').focus();
            return;
        }

        if (!assignedTo) {
            e.preventDefault();
            alert('Please select a user to assign this task to');
            document.getElementById('assigned_to').focus();
            return;
        }

        if (!priority) {
            e.preventDefault();
            alert('Please select a priority level');
            document.getElementById('priority').focus();
            return;
        }
    });
</script>
<?= $this->endSection() ?>
