<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */

// Default route - redirect to auth
$routes->get('/', 'Auth::index');

// Test route
$routes->get('test', function() {
    return 'Test route is working! Time: ' . date('Y-m-d H:i:s');
});

// Authentication routes
$routes->group('auth', function($routes) {
    $routes->get('login', 'Auth::login');
    $routes->post('login', 'Auth::login');
    $routes->get('logout', 'Auth::logout');
    $routes->get('register', 'Auth::register');
    $routes->post('register', 'Auth::register');
    $routes->get('test', 'Auth::test');
});

// User Dashboard routes
$routes->group('dashboard', function($routes) {
    $routes->get('/', 'Dashboard::index');
    $routes->get('tasks/(:segment)', 'Dashboard::tasks/$1');
    $routes->get('tasks', 'Dashboard::tasks');
    $routes->post('updateTask/(:num)', 'Dashboard::updateTask/$1');
    $routes->get('profile', 'Dashboard::profile');
    $routes->post('profile', 'Dashboard::profile');
});

// Admin routes
$routes->group('admin', function($routes) {
    $routes->get('/', 'Admin::index');
    $routes->get('users', 'Admin::users');
    $routes->get('users/create', 'Admin::createUser');
    $routes->post('users/create', 'Admin::createUser');
    $routes->get('users/edit/(:num)', 'Admin::editUser/$1');
    $routes->post('users/edit/(:num)', 'Admin::editUser/$1');
    $routes->get('users/delete/(:num)', 'Admin::deleteUser/$1');

    $routes->get('tasks', 'Admin::tasks');
    $routes->get('tasks/create', 'Admin::createTask');
    $routes->post('tasks/create', 'Admin::createTask');
    $routes->get('tasks/edit/(:num)', 'Admin::editTask/$1');
    $routes->post('tasks/edit/(:num)', 'Admin::editTask/$1');
    $routes->get('tasks/delete/(:num)', 'Admin::deleteTask/$1');
});
